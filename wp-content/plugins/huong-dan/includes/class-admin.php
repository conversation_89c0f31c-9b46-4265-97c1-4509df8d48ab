<?php
/**
 * Admin functionality for Huong Dan plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

class HuongDan_Admin {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_huong_dan_update_menu_order', array($this, 'ajax_update_menu_order'));
        add_action('wp_ajax_huong_dan_delete_page', array($this, 'ajax_delete_page'));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'Hướng dẫn SePay',
            'Hướng dẫn',
            'manage_options',
            'huong-dan',
            array($this, 'admin_page'),
            'dashicons-book-alt',
            30
        );
        
        // Add menu management submenu
        add_submenu_page(
            'huong-dan',
            'Quản lý Men<PERSON>',
            'Quản lý <PERSON>',
            'manage_options',
            'huong-dan-menu',
            array($this, 'menu_manager')
        );

        // Add submenu pages for ALL pages (both parent and child)
        $pages = HuongDan_Database::get_all_pages();
        foreach ($pages as $page) {
            add_submenu_page(
                'huong-dan',
                $page->title,
                $page->title,
                'manage_options',
                'huong-dan-' . $page->slug,
                array($this, 'edit_page')
            );
        }

        // Add new page submenu
        add_submenu_page(
            'huong-dan',
            'Thêm trang mới',
            'Thêm trang mới',
            'manage_options',
            'huong-dan-new',
            array($this, 'new_page')
        );

        // Add debug submenu
        add_submenu_page(
            'huong-dan',
            'Debug Info',
            'Debug Info',
            'manage_options',
            'huong-dan-debug',
            array($this, 'debug_page')
        );
    }
    
    public function admin_init() {
        // Register settings
        register_setting('huong_dan_settings', 'huong_dan_options');
        
        // Handle form submissions
        if (isset($_POST['huong_dan_save'])) {
            $this->save_page();
        }
        
        if (isset($_POST['huong_dan_delete'])) {
            $this->delete_page();
        }

        if (isset($_POST['reset_sample_data'])) {
            $this->reset_sample_data();
        }
    }
    
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'huong-dan') !== false) {
            wp_enqueue_editor();
            wp_enqueue_script('jquery');
            wp_enqueue_script('jquery-ui-core');
            wp_enqueue_script('jquery-ui-widget');
            wp_enqueue_script('jquery-ui-mouse');
            wp_enqueue_script('jquery-ui-sortable');

            // Enqueue jQuery UI CSS
            wp_enqueue_style('jquery-ui-style', 'https://code.jquery.com/ui/1.12.1/themes/ui-lightness/jquery-ui.css');

            // Enqueue custom admin script for menu management
            wp_enqueue_script(
                'huong-dan-admin',
                HUONG_DAN_PLUGIN_URL . 'assets/admin.js',
                array('jquery', 'jquery-ui-core', 'jquery-ui-widget', 'jquery-ui-mouse', 'jquery-ui-sortable'),
                HUONG_DAN_VERSION . '-' . time(), // Add timestamp to prevent caching during development
                true
            );

            // Localize script for AJAX
            wp_localize_script('huong-dan-admin', 'huongDanAjax', array(
                'ajaxurl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('huong_dan_menu_nonce')
            ));

            // Enqueue admin CSS
            wp_enqueue_style(
                'huong-dan-admin',
                HUONG_DAN_PLUGIN_URL . 'assets/admin.css',
                array(),
                HUONG_DAN_VERSION . '-' . time() // Add timestamp to prevent caching during development
            );
        }
    }
    
    public function admin_page() {
        $pages = HuongDan_Database::get_all_pages();

        // Check if table exists
        global $wpdb;
        $table_name = $wpdb->prefix . 'huong_dan_pages';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

        ?>
        <div class="wrap">
            <h1>Quản lý Hướng dẫn SePay</h1>

            <?php
            // Show messages
            if (isset($_GET['message'])) {
                switch ($_GET['message']) {
                    case 'sample_data_created':
                        echo '<div class="notice notice-success is-dismissible"><p>Dữ liệu mẫu đã được tạo thành công!</p></div>';
                        break;
                    case 'created':
                        echo '<div class="notice notice-success is-dismissible"><p>Trang đã được tạo thành công!</p></div>';
                        break;
                    case 'updated':
                        echo '<div class="notice notice-success is-dismissible"><p>Trang đã được cập nhật thành công!</p></div>';
                        break;
                    case 'deleted':
                        echo '<div class="notice notice-success is-dismissible"><p>Trang đã được xóa thành công!</p></div>';
                        break;
                }
            }
            ?>

            <?php if (!$table_exists): ?>
            <div class="notice notice-error">
                <p><strong>Lỗi:</strong> Bảng database chưa được tạo. Hãy thử tắt và bật lại plugin.</p>
            </div>
            <?php endif; ?>

            <?php if (empty($pages)): ?>
            <div class="notice notice-warning">
                <p><strong>Chưa có dữ liệu:</strong> Chưa có trang nào được tạo.</p>
                <form method="post" style="display: inline;">
                    <?php wp_nonce_field('reset_sample_data', 'reset_nonce'); ?>
                    <input type="submit" name="reset_sample_data" class="button button-secondary" value="Tạo dữ liệu mẫu" onclick="return confirm('Bạn có chắc chắn muốn tạo dữ liệu mẫu?')">
                </form>
            </div>
            <?php endif; ?>

            <?php if (!empty($pages)): ?>
            <div class="card">
                <h2>Danh sách trang hướng dẫn (<?php echo count($pages); ?> trang)</h2>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>Tiêu đề</th>
                            <th>Slug</th>
                            <th>Loại</th>
                            <th>Thứ tự</th>
                            <th>Ngày tạo</th>
                            <th>Hành động</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($pages as $page): ?>
                        <tr>
                            <td>
                                <strong><?php echo esc_html($page->title); ?></strong>
                                <?php if ($page->parent_id > 0): ?>
                                    <br><small style="color: #666;">└ Mục con</small>
                                <?php endif; ?>
                            </td>
                            <td><?php echo esc_html($page->slug); ?></td>
                            <td>
                                <?php if ($page->is_parent): ?>
                                    <span class="dashicons dashicons-category" title="Mục cha"></span> Mục cha
                                <?php else: ?>
                                    <span class="dashicons dashicons-admin-page" title="Trang"></span> Trang
                                <?php endif; ?>
                            </td>
                            <td><?php echo esc_html($page->menu_order); ?></td>
                            <td><?php echo esc_html($page->created_at); ?></td>
                            <td>
                                <?php if ($page->has_link): ?>
                                    <a href="<?php echo admin_url('admin.php?page=huong-dan-' . $page->slug); ?>" class="button button-small">Chỉnh sửa</a>
                                    <a href="<?php echo home_url('/huong-dan/' . $page->slug); ?>" class="button button-small" target="_blank">Xem</a>
                                <?php else: ?>
                                    <span style="color: #666;">Mục cha (không có link)</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>

            <p>
                <a href="<?php echo admin_url('admin.php?page=huong-dan-new'); ?>" class="button button-primary">Thêm trang mới</a>
                <a href="<?php echo admin_url('admin.php?page=huong-dan-menu'); ?>" class="button button-secondary">Quản lý Menu</a>

                <?php if (!empty($pages)): ?>
                <form method="post" style="display: inline; margin-left: 10px;">
                    <?php wp_nonce_field('reset_sample_data', 'reset_nonce'); ?>
                    <input type="submit" name="reset_sample_data" class="button" value="Tạo lại dữ liệu mẫu" onclick="return confirm('Điều này sẽ xóa tất cả dữ liệu hiện tại và tạo lại. Bạn có chắc chắn?')">
                </form>
                <?php endif; ?>
            </p>
        </div>
        <?php
    }
    
    public function edit_page() {
        $slug = str_replace('huong-dan-', '', $_GET['page']);
        $page = HuongDan_Database::get_page($slug);
        
        if (!$page) {
            echo '<div class="wrap"><h1>Không tìm thấy trang</h1></div>';
            return;
        }
        
        $this->render_page_form($page);
    }
    
    public function new_page() {
        $page = (object) array(
            'id' => 0,
            'slug' => '',
            'title' => '',
            'content' => '',
            'parent_id' => 0,
            'menu_order' => 0,
            'depth' => 0,
            'is_parent' => 0,
            'has_link' => 1,
            'icon' => ''
        );

        $this->render_page_form($page, true);
    }

    public function menu_manager() {
        $hierarchical_pages = HuongDan_Database::get_hierarchical_pages();
        ?>
        <div class="wrap">
            <h1>Quản lý Menu Hướng dẫn</h1>

            <div class="drag-instructions">
                <strong>Hướng dẫn:</strong> Kéo thả các mục menu để sắp xếp lại thứ tự. Các mục cha (không có link) sẽ hiển thị như tiêu đề phân nhóm. Sử dụng biểu tượng ⋮⋮ để kéo thả.
            </div>

            <div class="menu-manager-container">
                <div id="menu-sortable" class="menu-list">
                    <?php $this->render_menu_items($hierarchical_pages); ?>
                </div>

                <div class="menu-actions">
                    <button type="button" id="save-menu-order" class="button button-primary">Lưu thứ tự</button>
                    <button type="button" id="test-sortable" class="button button-secondary">Test Sortable</button>
                    <span id="save-status" class="save-status"></span>
                </div>

                <script>
                jQuery(document).ready(function($) {
                    $('#test-sortable').on('click', function() {
                        console.log('Test button clicked');
                        console.log('jQuery version:', $.fn.jquery);
                        console.log('jQuery UI Sortable available:', typeof $.fn.sortable !== 'undefined');
                        console.log('Menu items found:', $('.menu-item').length);

                        if (typeof $.fn.sortable !== 'undefined') {
                            alert('jQuery UI Sortable is loaded! Check console for details.');
                        } else {
                            alert('jQuery UI Sortable is NOT loaded!');
                        }
                    });
                });
                </script>
            </div>
        </div>

        <style>
        .menu-manager-container {
            max-width: 800px;
            margin-top: 20px;
        }

        .menu-list {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 0;
        }

        .menu-item {
            border-bottom: 1px solid #f0f0f1;
            padding: 15px;
            cursor: move;
            position: relative;
            background: #fff;
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-item.parent-item {
            background: #f8f9fa;
            font-weight: 600;
            color: #1d2327;
            cursor: default;
        }

        .menu-item.child-item {
            padding-left: 40px;
            background: #fff;
        }

        .menu-item.ui-sortable-helper {
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            border: 1px solid #007cba;
        }

        .menu-item.ui-sortable-placeholder {
            background: #e1f5fe;
            border: 2px dashed #007cba;
            height: 50px;
        }

        .menu-item-title {
            font-size: 14px;
            margin: 0;
        }

        .menu-item-info {
            font-size: 12px;
            color: #646970;
            margin-top: 5px;
        }

        .menu-item-actions {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
        }

        .menu-item-actions a {
            margin-left: 10px;
            text-decoration: none;
        }

        .menu-actions {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .save-status {
            margin-left: 10px;
            font-weight: 600;
        }

        .save-status.success {
            color: #00a32a;
        }

        .save-status.error {
            color: #d63638;
        }
        </style>
        <?php
    }

    private function render_menu_items($pages, $depth = 0) {
        foreach ($pages as $page) {
            $item_class = $page->is_parent ? 'parent-item' : 'child-item';
            $has_link_text = $page->has_link ? 'Có link' : 'Không có link';
            ?>
            <div class="menu-item <?php echo $item_class; ?>" data-id="<?php echo $page->id; ?>" data-parent-id="<?php echo $page->parent_id; ?>" data-depth="<?php echo $depth; ?>">
                <div class="menu-item-title">
                    <?php if ($page->icon): ?>
                        <i class="<?php echo esc_attr($page->icon); ?>"></i>
                    <?php endif; ?>
                    <?php echo esc_html($page->title); ?>
                </div>
                <div class="menu-item-info">
                    Slug: <?php echo esc_html($page->slug); ?> | <?php echo $has_link_text; ?>
                </div>
                <div class="menu-item-actions">
                    <a href="<?php echo admin_url('admin.php?page=huong-dan-' . $page->slug); ?>" class="button button-small">Chỉnh sửa</a>
                    <?php if ($page->has_link): ?>
                        <a href="<?php echo home_url('/huong-dan/' . $page->slug); ?>" class="button button-small" target="_blank">Xem</a>
                    <?php endif; ?>
                    <button type="button" class="button button-small delete-page-btn" data-page-id="<?php echo $page->id; ?>" data-page-title="<?php echo esc_attr($page->title); ?>">Xóa</button>
                </div>
            </div>
            <?php
            if (!empty($page->children)) {
                $this->render_menu_items($page->children, $depth + 1);
            }
        }
    }

    public function ajax_update_menu_order() {
        check_ajax_referer('huong_dan_menu_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $menu_data = json_decode(stripslashes($_POST['menu_data']), true);

        if (!$menu_data) {
            wp_send_json_error('Invalid menu data');
        }

        HuongDan_Database::update_menu_order($menu_data);

        wp_send_json_success('Menu order updated successfully');
    }

    private function reset_sample_data() {
        if (!wp_verify_nonce($_POST['reset_nonce'], 'reset_sample_data')) {
            wp_die('Nonce verification failed');
        }

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'huong_dan_pages';

        // Clear existing data
        $wpdb->query("TRUNCATE TABLE $table_name");

        // Create sample data
        $plugin = new HuongDanPlugin();
        $plugin->create_default_pages();

        // Redirect with success message
        wp_redirect(admin_url('admin.php?page=huong-dan&message=sample_data_created'));
        exit;
    }

    public function debug_page() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'huong_dan_pages';

        ?>
        <div class="wrap">
            <h1>Debug Information</h1>

            <div class="card">
                <h2>Database Status</h2>
                <?php
                $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
                echo '<p><strong>Table exists:</strong> ' . ($table_exists ? 'Yes' : 'No') . '</p>';

                if ($table_exists) {
                    $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
                    echo '<p><strong>Total records:</strong> ' . $count . '</p>';

                    $structure = $wpdb->get_results("DESCRIBE $table_name");
                    echo '<h3>Table Structure:</h3>';
                    echo '<table class="wp-list-table widefat">';
                    echo '<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>';
                    echo '<tbody>';
                    foreach ($structure as $column) {
                        echo '<tr>';
                        echo '<td>' . $column->Field . '</td>';
                        echo '<td>' . $column->Type . '</td>';
                        echo '<td>' . $column->Null . '</td>';
                        echo '<td>' . $column->Key . '</td>';
                        echo '<td>' . $column->Default . '</td>';
                        echo '</tr>';
                    }
                    echo '</tbody></table>';

                    if ($count > 0) {
                        $pages = $wpdb->get_results("SELECT * FROM $table_name ORDER BY parent_id, menu_order");
                        echo '<h3>All Records:</h3>';
                        echo '<table class="wp-list-table widefat">';
                        echo '<thead><tr><th>ID</th><th>Title</th><th>Slug</th><th>Parent ID</th><th>Is Parent</th><th>Has Link</th><th>Menu Order</th></tr></thead>';
                        echo '<tbody>';
                        foreach ($pages as $page) {
                            echo '<tr>';
                            echo '<td>' . $page->id . '</td>';
                            echo '<td>' . $page->title . '</td>';
                            echo '<td>' . $page->slug . '</td>';
                            echo '<td>' . $page->parent_id . '</td>';
                            echo '<td>' . ($page->is_parent ? 'Yes' : 'No') . '</td>';
                            echo '<td>' . ($page->has_link ? 'Yes' : 'No') . '</td>';
                            echo '<td>' . $page->menu_order . '</td>';
                            echo '</tr>';
                        }
                        echo '</tbody></table>';
                    }
                }
                ?>
            </div>

            <div class="card">
                <h2>Plugin Status</h2>
                <p><strong>Plugin activated:</strong> <?php echo get_option('huong_dan_activated') ? 'Yes' : 'No'; ?></p>
                <p><strong>WordPress version:</strong> <?php echo get_bloginfo('version'); ?></p>
                <p><strong>PHP version:</strong> <?php echo PHP_VERSION; ?></p>
                <p><strong>Plugin path:</strong> <?php echo HUONG_DAN_PLUGIN_PATH; ?></p>
                <p><strong>Plugin URL:</strong> <?php echo HUONG_DAN_PLUGIN_URL; ?></p>
            </div>

            <div class="card">
                <h2>Actions</h2>
                <form method="post">
                    <?php wp_nonce_field('debug_actions', 'debug_nonce'); ?>
                    <p>
                        <input type="submit" name="recreate_table" class="button" value="Recreate Table" onclick="return confirm('This will recreate the database table. Continue?')">
                        <input type="submit" name="reset_sample_data" class="button" value="Reset Sample Data" onclick="return confirm('This will reset all sample data. Continue?')">
                    </p>
                </form>
            </div>
        </div>
        <?php

        // Handle debug actions
        if (isset($_POST['recreate_table']) && wp_verify_nonce($_POST['debug_nonce'], 'debug_actions')) {
            $wpdb->query("DROP TABLE IF EXISTS $table_name");
            HuongDan_Database::create_tables();
            echo '<div class="notice notice-success"><p>Table recreated!</p></div>';
        }
    }
    
    private function render_page_form($page, $is_new = false) {
        ?>
        <div class="wrap">
            <h1><?php echo $is_new ? 'Thêm trang mới' : 'Chỉnh sửa: ' . esc_html($page->title); ?></h1>
            
            <form method="post" action="">
                <?php wp_nonce_field('huong_dan_save', 'huong_dan_nonce'); ?>
                <input type="hidden" name="page_id" value="<?php echo esc_attr($page->id); ?>">
                <input type="hidden" name="is_new" value="<?php echo $is_new ? '1' : '0'; ?>">
                
                <table class="form-table">
                    <tr>
                        <th scope="row">Tiêu đề</th>
                        <td>
                            <input type="text" name="title" value="<?php echo esc_attr($page->title); ?>" class="regular-text" required>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Slug</th>
                        <td>
                            <input type="text" name="slug" value="<?php echo esc_attr($page->slug); ?>" class="regular-text" required>
                            <p class="description">URL thân thiện (chỉ chữ thường, số và dấu gạch ngang)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Loại mục</th>
                        <td>
                            <label>
                                <input type="radio" name="is_parent" value="1" <?php checked($page->is_parent, 1); ?> onchange="togglePageType()">
                                Mục cha (tiêu đề nhóm, không có link)
                            </label><br>
                            <label>
                                <input type="radio" name="is_parent" value="0" <?php checked($page->is_parent, 0); ?> onchange="togglePageType()">
                                Mục con (có link đến trang)
                            </label>
                        </td>
                    </tr>
                    <tr id="parent_select_row" style="<?php echo $page->is_parent ? 'display:none' : ''; ?>">
                        <th scope="row">Mục cha</th>
                        <td>
                            <select name="parent_id" class="regular-text">
                                <option value="0">-- Không có mục cha --</option>
                                <?php
                                $parent_pages = HuongDan_Database::get_all_pages();
                                foreach ($parent_pages as $parent_page) {
                                    if ($parent_page->is_parent && $parent_page->id != $page->id) {
                                        $selected = selected($page->parent_id, $parent_page->id, false);
                                        echo '<option value="' . $parent_page->id . '" ' . $selected . '>' . esc_html($parent_page->title) . '</option>';
                                    }
                                }
                                ?>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Icon</th>
                        <td>
                            <input type="text" name="icon" value="<?php echo esc_attr($page->icon); ?>" class="regular-text" placeholder="fas fa-home">
                            <p class="description">Font Awesome icon class (ví dụ: fas fa-home, fab fa-facebook)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Thứ tự menu</th>
                        <td>
                            <input type="number" name="menu_order" value="<?php echo esc_attr($page->menu_order); ?>" class="small-text">
                        </td>
                    </tr>
                    <tr id="content_row" style="<?php echo $page->is_parent ? 'display:none' : ''; ?>">
                        <th scope="row">Nội dung</th>
                        <td>
                            <?php
                            wp_editor($page->content, 'page_content', array(
                                'textarea_name' => 'content',
                                'media_buttons' => true,
                                'textarea_rows' => 20,
                                'teeny' => false,
                                'tinymce' => true,
                                'quicktags' => true
                            ));
                            ?>
                        </td>
                    </tr>
                </table>

                <script>
                function togglePageType() {
                    var isParent = document.querySelector('input[name="is_parent"]:checked').value == '1';
                    var parentRow = document.getElementById('parent_select_row');
                    var contentRow = document.getElementById('content_row');

                    if (isParent) {
                        parentRow.style.display = 'none';
                        contentRow.style.display = 'none';
                        document.querySelector('select[name="parent_id"]').value = '0';
                    } else {
                        parentRow.style.display = 'table-row';
                        contentRow.style.display = 'table-row';
                    }
                }
                </script>
                
                <p class="submit">
                    <input type="submit" name="huong_dan_save" class="button-primary" value="<?php echo $is_new ? 'Tạo trang' : 'Cập nhật'; ?>">
                    <?php if (!$is_new): ?>
                    <input type="submit" name="huong_dan_delete" class="button" value="Xóa trang" onclick="return confirm('Bạn có chắc chắn muốn xóa trang này?')">
                    <?php endif; ?>
                </p>
            </form>
        </div>
        <?php
    }
    
    private function save_page() {
        if (!wp_verify_nonce($_POST['huong_dan_nonce'], 'huong_dan_save')) {
            wp_die('Nonce verification failed');
        }

        $page_id = intval($_POST['page_id']);
        $is_new = $_POST['is_new'] === '1';
        $title = sanitize_text_field($_POST['title']);
        $slug = sanitize_title($_POST['slug']);
        $content = wp_kses_post($_POST['content']);
        $menu_order = intval($_POST['menu_order']);
        $parent_id = intval($_POST['parent_id']);
        $is_parent = intval($_POST['is_parent']);
        $icon = sanitize_text_field($_POST['icon']);
        $has_link = $is_parent ? 0 : 1; // Parent items don't have links

        if ($is_new) {
            HuongDan_Database::create_page($slug, $title, $content, $menu_order, $parent_id, $is_parent, $has_link, $icon);
            if ($has_link) {
                wp_redirect(admin_url('admin.php?page=huong-dan-' . $slug . '&message=created'));
            } else {
                wp_redirect(admin_url('admin.php?page=huong-dan&message=created'));
            }
        } else {
            HuongDan_Database::update_page($page_id, array(
                'title' => $title,
                'slug' => $slug,
                'content' => $content,
                'parent_id' => $parent_id,
                'menu_order' => $menu_order,
                'is_parent' => $is_parent,
                'has_link' => $has_link,
                'icon' => $icon,
                'depth' => $parent_id > 0 ? 1 : 0
            ));
            if ($has_link) {
                wp_redirect(admin_url('admin.php?page=huong-dan-' . $slug . '&message=updated'));
            } else {
                wp_redirect(admin_url('admin.php?page=huong-dan&message=updated'));
            }
        }
        exit;
    }
    
    private function delete_page() {
        if (!wp_verify_nonce($_POST['huong_dan_nonce'], 'huong_dan_save')) {
            wp_die('Nonce verification failed');
        }
        
        $page_id = intval($_POST['page_id']);
        HuongDan_Database::delete_page($page_id);
        wp_redirect(admin_url('admin.php?page=huong-dan&message=deleted'));
        exit;
    }

    public function ajax_delete_page() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'huong_dan_menu_nonce')) {
            wp_die('Nonce verification failed');
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $page_id = intval($_POST['page_id']);

        if ($page_id <= 0) {
            wp_send_json_error('Invalid page ID');
            return;
        }

        // Check if page has children
        global $wpdb;
        $table_name = $wpdb->prefix . 'huong_dan_pages';
        $children_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE parent_id = %d AND status = 'active'",
            $page_id
        ));

        if ($children_count > 0) {
            wp_send_json_error('Không thể xóa mục cha có chứa mục con. Vui lòng xóa các mục con trước.');
            return;
        }

        // Delete the page
        $result = HuongDan_Database::delete_page($page_id);

        if ($result) {
            wp_send_json_success('Đã xóa trang thành công');
        } else {
            wp_send_json_error('Có lỗi xảy ra khi xóa trang');
        }
    }
}
